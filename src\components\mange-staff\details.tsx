import React, { useState, useEffect } from 'react';
import { Modal } from '@/components/common/modal';
import { ModalProps } from '../types';
import { Button } from '@/components/ui/button';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';
import { Loader2, User, Award, ClipboardCheck } from 'lucide-react';
import dayjs from 'dayjs';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { StatusBadge } from '@/components/common/status-badge';
import { numberFormat } from '@/lib/utils';
import { Copy } from 'lucide-react';

// Tab interface
interface TabProps {
  label: string;
  icon: React.ReactNode;
  content: React.ReactNode;
}

const Details: React.FC<ModalProps> = ({ setOpen, open, mutate, data }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState(0);

  // Reset active tab when modal opens
  useEffect(() => {
    if (open) {
      setActiveTab(0);
    }
  }, [open]);

  // Early return if no data
  if (!data) {
    return null;
  }

  // Function to toggle staff active status
  const handleToggleStatus = async () => {
    if (!data?.id) {
      toast.error('Staff ID is missing');
      return;
    }

    try {
      setIsLoading(true);
      const res = await myApi.patch('/staff/update', {
        id: data.id,
        isActive: !data.isActive,
      });
      setIsLoading(false);
      if (res.status === 200) {
        toast.success(res.data.message || 'Staff status updated successfully');
        setOpen(false);
        if (mutate) {
          mutate();
        }
      }
    } catch (error) {
      setIsLoading(false);
      toast.error('Failed to update staff status');
    }
  };

  // Personal Information Tab Content
  const PersonalInfo = () => (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Personal Details Column */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-foreground mb-3">Personal Details</h3>

        <div>
          <p className="text-sm font-medium">Full Name</p>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {data?.fullName || 'N/A'}
          </p>
        </div>

        <div>
          <p className="text-sm font-medium">Email</p>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {data?.email || 'N/A'}
          </p>
        </div>

        <div>
          <p className="text-sm font-medium">Phone Number</p>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {data?.phoneNumber || 'N/A'}
          </p>
        </div>

        <div>
          <p className="text-sm font-medium">Staff ID</p>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {data?.staffCode || data?.staffId || 'N/A'}
          </p>
        </div>

        <div>
          <p className="text-sm font-medium">Status</p>
          <div className="mt-1">
            {data?.isActive !== undefined
              ? data.isActive
                ? StatusBadge({ status: 'active' })
                : StatusBadge({ status: 'inactive' })
              : 'N/A'}
          </div>
        </div>
      </div>

      {/* Professional Details Column */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-foreground mb-3">Professional Details</h3>

        <div>
          <p className="text-sm font-medium">Location</p>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {data?.location?.name && data?.location?.region?.name
              ? `${data.location.name}, ${data.location.region.name}`
              : data?.location?.name && data?.location?.region
              ? `${data.location.name}, ${data.location.region}`
              : data?.location?.name || 'N/A'}
          </p>
        </div>

        <div>
          <p className="text-sm font-medium">Staff Type</p>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {data?.type || 'N/A'}
          </p>
        </div>

        <div>
          <p className="text-sm font-medium">Department</p>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {data?.department?.name || 'N/A'}
          </p>
        </div>

        {data?.unitId && data?.unit && (
          <div>
            <p className="text-sm font-medium">Unit</p>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {data.unit.name}
            </p>
          </div>
        )}

        {data?.isDoctor && (
          <>
            <div>
              <p className="text-sm font-medium">Consultant Status</p>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {data.isConsultant
                  ? data.isVisitingConsultant
                    ? 'Visiting Consultant'
                    : 'Consultant'
                  : 'Doctor'}
              </p>
            </div>
            {data?.specialty && (
              <div>
                <p className="text-sm font-medium">Specialty</p>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {data.specialty}
                </p>
              </div>
            )}
          </>
        )}

        <div>
          <p className="text-sm font-medium">Date Created</p>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {data?.createdAt ? dayjs(data.createdAt).format('MMMM D, YYYY') : 'N/A'}
          </p>
        </div>
      </div>
    </div>
  );

  // Referral Information Tab Content
  const ReferralInfo = () => (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Referral Information Column */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-foreground mb-3">Referral Information</h3>

        <div>
          <p className="text-sm font-medium">Referral Code</p>
          <div className="flex items-center gap-2 mt-1">
            <Badge variant="outline" className="px-3 py-1 text-sm">
              {data?.referralCode?.code || 'N/A'}
            </Badge>
            {data?.referralCode?.code && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  navigator.clipboard.writeText(data.referralCode?.code || '');
                  toast.success('Referral code copied to clipboard');
                }}
              >
                <Copy className="w-4 h-4" />
              </Button>
            )}
          </div>
        </div>

        <div>
          <p className="text-sm font-medium">Code Usage</p>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {data?.codeUsage || '0'} times
          </p>
        </div>
      </div>

      {/* Financial Information Column */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-foreground mb-3">Financial Information</h3>

        <div>
          <p className="text-sm font-medium">Total Amount Generated</p>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {data?.total ? numberFormat(data.total) : '₦0.00'}
          </p>
        </div>

        <div>
          <p className="text-sm font-medium">Wallet Balance</p>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {data?.wallet ? numberFormat(data.wallet) : '₦0.00'}
          </p>
        </div>

        {data?.creditLimit !== undefined && (
          <div>
            <p className="text-sm font-medium">Credit Limit</p>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {numberFormat(data.creditLimit)}
            </p>
          </div>
        )}

        {data?.monthlyCreditUsed !== undefined && (
          <div>
            <p className="text-sm font-medium">Monthly Credit Used</p>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {numberFormat(data.monthlyCreditUsed)}
            </p>
          </div>
        )}
      </div>
    </div>
  );

  const tabs: TabProps[] = [
    {
      label: 'Personal & Professional',
      icon: <User className="w-4 h-4" />,
      content: <PersonalInfo />,
    },
    {
      label: 'Referral & Financial',
      icon: <ClipboardCheck className="w-4 h-4" />,
      content: <ReferralInfo />,
    },
  ];

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      title="Staff Details"
      description={`Comprehensive information for ${data?.fullName || 'Unknown Staff'}`}
      size="lg"
    >
      {/* Tabs */}
      <div className="flex border-b mb-4">
        {tabs.map((tab, index) => (
          <button
            key={index}
            className={cn(
              'flex items-center px-2 md:px-4 py-2 text-sm font-medium transition-colors',
              activeTab === index
                ? 'border-b-2 border-primary text-primary'
                : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            )}
            onClick={() => setActiveTab(index)}
          >
            {tab.icon}
            {tab.label}
          </button>
        ))}
      </div>

      {/* Tab content */}
      <div className="py-2 mb-6">{tabs[activeTab].content}</div>

      {/* Action buttons */}
      <div className="flex justify-end gap-2 mt-6 pt-4 border-t">
        <Button variant="outline" onClick={() => setOpen(false)}>
          Close
        </Button>
        <Button
          variant={data?.isActive ? 'destructive' : 'default'}
          onClick={handleToggleStatus}
          disabled={isLoading}
        >
          {isLoading ? (
            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
          ) : null}
          {data?.isActive ? 'Deactivate Staff' : 'Activate Staff'}
        </Button>
      </div>
    </Modal>
  );
};

export default Details;
